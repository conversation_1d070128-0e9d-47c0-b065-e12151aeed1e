#!/usr/bin/env python3
"""
Script to run Alembic migrations
"""

import subprocess
import sys
import os

def run_migration():
    """Run the Alembic migration to add search_space_id and user_id to chats table"""
    
    # Change to the backend directory
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(backend_dir)
    
    try:
        # Run the migration
        print("Running Alembic migration...")
        result = subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", "head"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Migration completed successfully!")
            print("Output:", result.stdout)
        else:
            print("❌ Migration failed!")
            print("Error:", result.stderr)
            print("Output:", result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ Error running migration: {e}")
        return False
    
    return True

def check_migration_status():
    """Check current migration status"""
    try:
        print("Checking current migration status...")
        result = subprocess.run([
            sys.executable, "-m", "alembic", "current"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Current migration status:")
            print(result.stdout)
        else:
            print("Error checking status:", result.stderr)
            
    except Exception as e:
        print(f"Error checking migration status: {e}")

if __name__ == "__main__":
    print("🚀 Starting migration process...")
    
    # Check current status
    check_migration_status()
    
    # Run migration
    if run_migration():
        print("\n✅ Migration process completed successfully!")
        
        # Check status again
        print("\n📊 Final migration status:")
        check_migration_status()
    else:
        print("\n❌ Migration process failed!")
        sys.exit(1)
